<?php

declare(strict_types=1);

namespace App\Infrastructure\UserManagementApi\Resource;

use App\Domain\User\Constraints;
use App\Infrastructure\UserManagementApi\DTO\UserCreateRequest;
use App\Infrastructure\UserManagementApi\DTO\UserPasswordUpdateRequest;
use App\Infrastructure\UserManagementApi\DTO\UserResponse;
use App\Infrastructure\UserManagementApi\DTO\UserUpdateRequest;
use App\Infrastructure\UserManagementApi\HttpEndpoint\UserEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Operation\Put;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Attribute\Value\Response;
use PreZero\ApiBundle\Enum\ContentType;

#[ApiResource(
    area: 'user-management',
    operations: [
        new Get(
            controller: UserEndpoint::class,
            controllerAction: 'checkUsername',
            uriTemplate: '/user/check-username/{username}',
            urlRequirements: ['username' => Constraints::USERNAME_REGEX],
            pathParameters: [
                new PathParameter(
                    name: 'username',
                    description: 'The username to check',
                ),
            ],
            responseType: ContentType::EMPTY,
        ),
        new Get(
            controller: UserEndpoint::class,
            controllerAction: 'checkEmail',
            uriTemplate: '/user/check-email/{email}',
            urlRequirements: ['email' => Constraints::EMAIL_REGEX],
            pathParameters: [
                new PathParameter(
                    name: 'email',
                    description: 'The email to check',
                ),
            ],
            responseType: ContentType::EMPTY,
        ),
        new Get(
            controller: UserEndpoint::class,
            controllerAction: 'getByUsername',
            uriTemplate: '/user/{username}',
            urlRequirements: ['username' => Constraints::USERNAME_REGEX],
            pathParameters: [
                new PathParameter(
                    name: 'username',
                    description: 'The username of the user to retrieve',
                ),
            ],
            output: UserResponse::class,
        ),
        new Post(
            controller: UserEndpoint::class,
            controllerAction: 'createUser',
            uriTemplate: '/users',
            input: UserCreateRequest::class,
            responses: [
                new Response(httpCode: 409, description: 'User already exists', contentType: ContentType::EMPTY),
            ],
            output: UserResponse::class,
        ),
        new Put(
            controller: UserEndpoint::class,
            controllerAction: 'updateUser',
            uriTemplate: '/users/{username}',
            urlRequirements: ['username' => Constraints::USERNAME_REGEX],
            pathParameters: [
                new PathParameter(
                    name: 'username',
                    description: 'The username of the user to update',
                ),
            ],
            input: UserUpdateRequest::class,
            output: UserResponse::class,
        ),
        new Put(
            controller: UserEndpoint::class,
            controllerAction: 'updateUserPassword',
            uriTemplate: '/users/{username}/password',
            urlRequirements: ['username' => Constraints::USERNAME_REGEX],
            pathParameters: [
                new PathParameter(
                    name: 'username',
                    description: 'The username of the user whose password to update',
                ),
            ],
            input: UserPasswordUpdateRequest::class,
            responseType: ContentType::EMPTY,
        ),
    ],
    identifier: null,
    tag: 'User Management',
    security: 'is_granted("ROLE_FULL_ACCESS")'
)]
class User
{
}
