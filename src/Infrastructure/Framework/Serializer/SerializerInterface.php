<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Serializer;

interface SerializerInterface
{
    /**
     * Serializes data in the appropriate format.
     *
     * @param array<string, scalar|string[]> $context Options normalizers/encoders have access to
     *
     * @throws SerializerException
     */
    public function serialize(mixed $data, string $format, array $context = []): string;

    /**
     * Deserializes data into the given type.
     *
     * @template T of object
     *
     * @param class-string<T>|string         $type
     * @param array<string, scalar|string[]> $context
     *
     * @phpstan-return ($type is class-string<T> ? T : mixed)
     *
     * @throws SerializerException
     */
    public function deserialize(string $data, string $type, string $format, array $context = []): mixed;

    /**
     * Denormalized data into the given type.
     *
     * @template T of object
     *
     * @param class-string<T>                $type
     * @param array<string, scalar|string[]> $context
     *
     * @phpstan-return T
     *
     * @throws SerializerException
     */
    public function denormalize(mixed $data, string $type, array $context = []): object;

    /**
     * Deserializes data into the given object.
     *
     * @template T of object
     *
     * @param class-string<T>                $className
     * @param array<string, scalar|string[]> $context
     *
     * @phpstan-return T
     *
     * @throws SerializerException
     */
    public function deserializeIntoObject(
        string $data,
        string $className,
        string $format = 'json',
        array $context = [],
    ): object;

    /**
     * Deserializes data into an array of objects.
     *
     * @template T of object
     *
     * @param class-string<T>                $className
     * @param array<string, scalar|string[]> $context
     *
     * @phpstan-return array<T>
     *
     * @throws SerializerException
     */
    public function deserializeIntoArrayOfObjects(
        string $data,
        string $className,
        string $format = 'json',
        array $context = [],
    ): array;
}
