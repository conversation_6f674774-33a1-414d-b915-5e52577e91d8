<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Serializer;

use App\Infrastructure\Framework\Serializer\Exception\DeserializationException;
use App\Infrastructure\Framework\Serializer\Exception\SerializationException;
use Psr\Cache\CacheItemPoolInterface;
use Vuryss\Serializer\Metadata\CachedMetadataExtractor;
use Vuryss\Serializer\Metadata\MetadataExtractor;
use Vuryss\Serializer\SerializerException;

readonly class Serializer implements SerializerInterface
{
    private \Vuryss\Serializer\SerializerInterface $vuryssSerializer;

    public function __construct(
        private CacheItemPoolInterface $serializerCache,
    ) {
        $this->vuryssSerializer = new \Vuryss\Serializer\Serializer(
            metadataExtractor: new CachedMetadataExtractor(
                new MetadataExtractor(),
                $this->serializerCache
            ),
            attributes: [
                \Vuryss\Serializer\SerializerInterface::ATTRIBUTE_DATETIME_FORMAT => \DateTimeInterface::RFC3339_EXTENDED,
                \Vuryss\Serializer\SerializerInterface::ATTRIBUTE_SKIP_NULL_VALUES => true,
            ],
        );
    }

    /**
     * @throws SerializationException
     */
    public function serialize(mixed $data, string $format, array $context = []): string
    {
        try {
            return $this->vuryssSerializer->serialize($data, attributes: $context);
        } catch (SerializerException $e) {
            throw new SerializationException($e->getMessage(), previous: $e);
        }
    }

    /**
     * @throws DeserializationException
     */
    public function deserialize(string $data, string $type, string $format, array $context = []): mixed
    {
        try {
            return $this->vuryssSerializer->deserialize($data, $type, attributes: $context);
        } catch (SerializerException $e) {
            throw new DeserializationException($e->getMessage(), previous: $e);
        }
    }

    /**
     * Denormalized data into the given type.
     *
     * @template T of object
     *
     * @param class-string<T>                $type
     * @param array<string, scalar|string[]> $context
     *
     * @phpstan-return T
     *
     * @throws \App\Infrastructure\Framework\Serializer\SerializerException
     */
    public function denormalize(mixed $data, string $type, array $context = []): object
    {
        try {
            /** @var T $object */
            $object = $this->vuryssSerializer->denormalize($data, $type, attributes: $context);

            return $object;
        } catch (SerializerException $e) {
            throw new DeserializationException($e->getMessage(), previous: $e);
        }
    }

    public function deserializeIntoObject(
        string $data,
        string $className,
        string $format = 'json',
        array $context = [],
    ): object {
        try {
            return $this->vuryssSerializer->deserialize($data, $className, attributes: $context);
        } catch (SerializerException $e) {
            throw new DeserializationException($e->getMessage(), previous: $e);
        }
    }

    /**
     * Deserializes data into an array of objects.
     *
     * @template T of object
     *
     * @param class-string<T>                $className
     * @param array<string, scalar|string[]> $context
     *
     * @phpstan-return array<T>
     *
     * @throws DeserializationException
     */
    public function deserializeIntoArrayOfObjects(
        string $data,
        string $className,
        string $format = 'json',
        array $context = [],
    ): array {
        try {
            /** @var array<T> $array */
            $array = $this->vuryssSerializer->deserialize($data, $className.'[]', attributes: $context);

            return $array;
        } catch (SerializerException $e) {
            throw new DeserializationException($e->getMessage(), previous: $e);
        }
    }
}
